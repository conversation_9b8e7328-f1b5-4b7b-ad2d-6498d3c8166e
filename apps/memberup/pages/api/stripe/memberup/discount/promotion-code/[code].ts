import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPromotionCodesMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { code } = req.query
    console.log('Fetching promotion code:', code)

    const result = await stripeGetPromotionCodesMain(STRIPE_SECRET_KEY, {
      code: code as string,
    })

    console.log('Promotion code result:', JSON.stringify(result.data))

    // Check if we found any promotion codes
    if (result.data && result.data.length > 0) {
      const promoCode = result.data[0]

      // Check if max redemptions has been reached
      if (promoCode.max_redemptions !== null && promoCode.times_redeemed >= promoCode.max_redemptions) {
        return res.status(200).send({
          success: false,
          data: [],
          message: 'This promotional code has reached its maximum usage limit and is no longer available.',
        })
      }

      // Check if the coupon is valid
      if (promoCode.coupon && !promoCode.coupon.valid) {
        return res.status(200).send({
          success: false,
          data: [],
          message: 'This promotional code is no longer valid.',
        })
      }

      return res.status(200).send({
        success: true,
        data: result.data,
      })
    } else {
      // No promotion codes found with this code
      return res.status(200).send({
        success: false,
        data: [],
        message: 'No valid promotion code found',
      })
    }
  } catch (err: any) {
    console.log('Error fetching promotion code:', err)
    res.status(400).json({
      success: false,
      message: err.message,
      data: [],
    })
  }
})

export default handler
